{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 🏆 ABI Hierarchical Mixed-Effects Model Analysis\n", "\n", "## 🎯 Objective\n", "Analyze the effect of key promotion KPIs on **ABI MS Uplift (Relative)** using hierarchical mixed-effects modeling to account for retailer, brand, and pack variability.\n", "\n", "### 📊 Target KPIs:\n", "- **ABI_Duration_Days** - Promotion duration in days\n", "- **ABI_Coverage** - Store coverage percentage\n", "- **ABI_Mechanic** - Promotion mechanism (Immediate, LV, FID)\n", "- **Overlapping** - Binary indicator for overlapping competitive promotions\n", "- **Timing Variables** - Same Week, Before (1-2 wk), After (1-2 wk)\n", "- **Avg_Temp** - Average temperature during promotion\n", "- **ABI_vs_Segment_PTC_Index_Agg** - Price positioning vs segment\n", "\n", "### 🎯 Target Variable:\n", "- **ABI MS Uplift rel** - Relative market share uplift during promotion\n", "\n", "### 🏢 Hierarchical Groups:\n", "- **Retailer** effects (AUCHAN, CARREFOUR, etc.)\n", "- **Brand** effects (BUD, CORONA, LEFFE, etc.)\n", "- **Pack Type** effects (12-15 vs 20-24 packs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 📚 Import Libraries\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import warnings\n", "from datetime import datetime\n", "import statsmodels.formula.api as smf\n", "from statsmodels.regression.mixed_linear_model import MixedLM\n", "from scipy import stats\n", "from sklearn.preprocessing import StandardScaler\n", "\n", "# 🔧 Configure Settings\n", "warnings.filterwarnings('ignore')\n", "plt.style.use('default')\n", "sns.set_palette(\"husl\")\n", "plt.rcParams['figure.figsize'] = (12, 8)\n", "\n", "print(\"📚 Libraries imported successfully!\")\n", "print(\"🔧 Analysis environment configured\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 📊 Data Loading and Exploration"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 📥 Load the data\n", "df_raw = pd.read_csv(\"greater than 10 new_test_output_ads_v3.csv\")\n", "\n", "print(\"=\" * 80)\n", "print(\"📈 DATA OVERVIEW\")\n", "print(\"=\" * 80)\n", "print(f\"Dataset shape: {df_raw.shape}\")\n", "print(f\"Time period: {df_raw['ABI Start'].min()} to {df_raw['ABI End'].max()}\")\n", "\n", "# Show key columns\n", "key_cols = ['ABI PromoID', 'ABI SKU', 'Retailer', 'ABI Start', 'ABI End', \n", "           'ABI Coverage', 'ABI Mechanic', 'Overlapping', 'Same Week',\n", "           '1 wk before', '2 wk before', '1 wk after', '2 wk after',\n", "           'Avg Temp', 'ABI vs Segment PTC Index Agg', 'ABI MS Promo Uplift - rel']\n", "\n", "print(f\"\\n📋 Key columns for analysis ({len(key_cols)}):\")\n", "for i, col in enumerate(key_cols, 1):\n", "    print(f\"  {i:2d}. {col}\")\n", "\n", "print(f\"\\n📊 First few rows:\")\n", "display(df_raw[key_cols].head())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 🔍 Data Quality Check\n", "print(\"🔍 DATA QUALITY CHECK\")\n", "print(\"=\" * 50)\n", "\n", "print(\"\\n❌ Missing values in key columns:\")\n", "missing_summary = df_raw[key_cols].isnull().sum()\n", "missing_pct = (missing_summary / len(df_raw) * 100).round(2)\n", "\n", "for col in key_cols:\n", "    if missing_summary[col] > 0:\n", "        print(f\"  • {col}: {missing_summary[col]} ({missing_pct[col]}%)\")\n", "\n", "print(f\"\\n🎯 Target variable preview:\")\n", "target_col = 'ABI MS Promo Uplift - rel'\n", "print(f\"Target: {target_col}\")\n", "print(df_raw[target_col].describe())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. 🧹 Data Cleaning and Feature Engineering"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 🧹 Clean and prepare data\n", "df = df_raw.copy()\n", "\n", "# Handle missing values and infinities\n", "print(\"🔧 Handling missing values and infinities...\")\n", "df.replace([np.inf, -np.inf, \"\"], np.nan, inplace=True)\n", "\n", "# Remove duplicates\n", "print(f\"📊 Before deduplication: {len(df):,} rows\")\n", "df = df.drop_duplicates()\n", "print(f\"📊 After deduplication: {len(df):,} rows\")\n", "\n", "# Convert date columns and create duration\n", "print(\"📅 Processing date columns...\")\n", "df['ABI Start'] = pd.to_datetime(df['ABI Start'])\n", "df['ABI End'] = pd.to_datetime(df['ABI End'])\n", "df['ABI_Duration_Days'] = (df['ABI End'] - df['ABI Start']).dt.days\n", "\n", "print(f\"⏱️ Duration statistics:\")\n", "print(f\"  • Mean: {df['ABI_Duration_Days'].mean():.1f} days\")\n", "print(f\"  • Range: {df['ABI_Duration_Days'].min()} to {df['ABI_Duration_Days'].max()} days\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 🏷️ Extract brand and pack information\n", "print(\"🏷️ Extracting brand and pack information...\")\n", "df['Brand'] = df['ABI SKU'].str.extract(r'^([A-Z]+)')[0]\n", "df['Pack_12_15'] = df['ABI SKU'].str.contains(r'\\(12-15\\)', regex=False).astype(int)\n", "df['Pack_20_24'] = df['ABI SKU'].str.contains(r'\\(20-24\\)', regex=False).astype(int)\n", "df['Pack_Type'] = np.where(df['Pack_12_15'] == 1, '12-15', \n", "                          np.where(df['Pack_20_24'] == 1, '20-24', 'Other'))\n", "\n", "# ⏰ Create timing variables\n", "print(\"⏰ Creating timing variables...\")\n", "df['Before'] = ((df['1 wk before'].fillna(0) == 1) | \n", "               (df['2 wk before'].fillna(0) == 1)).astype(int)\n", "df['After'] = ((df['1 wk after'].fillna(0) == 1) | \n", "              (df['2 wk after'].fillna(0) == 1)).astype(int)\n", "df['Same_Week'] = df['Same Week'].fillna(0).astype(int)\n", "\n", "# 🔢 Clean numerical variables\n", "print(\"🔢 Processing numerical variables...\")\n", "df['ABI_Coverage'] = pd.to_numeric(df['ABI Coverage'], errors='coerce')\n", "df['Overlapping'] = pd.to_numeric(df['Overlapping'], errors='coerce').fillna(0)\n", "df['Avg_Temp'] = pd.to_numeric(df['Avg Temp'], errors='coerce')\n", "df['ABI_vs_Segment_PTC_Index_Agg'] = pd.to_numeric(df['ABI vs Segment PTC Index Agg'], errors='coerce')\n", "\n", "# 🎯 Process target variable\n", "df['ABI_MS_Uplift_Rel'] = pd.to_numeric(df['ABI MS Promo Uplift - rel'], errors='coerce')\n", "df = df.dropna(subset=['ABI_MS_Uplift_Rel'])\n", "\n", "# Handle extreme outliers\n", "uplift_95 = df['ABI_MS_Uplift_Rel'].quantile(0.95)\n", "outliers_before = (df['ABI_MS_Uplift_Rel'] > uplift_95 * 3).sum()\n", "df['ABI_MS_Uplift_Rel'] = np.where(df['ABI_MS_Uplift_Rel'] > uplift_95 * 3, \n", "                                  uplift_95 * 3, df['ABI_MS_Uplift_Rel'])\n", "print(f\"🎯 Target variable outliers capped: {outliers_before} extreme values\")\n", "\n", "print(f\"\\n✅ Feature engineering complete!\")\n", "print(f\"📊 Final dataset shape: {df.shape}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 📏 Standardize variables and prepare for modeling\n", "print(\"📏 Standardizing continuous variables...\")\n", "\n", "# Convert categorical variables\n", "df['Retailer'] = df['Retailer'].astype('category')\n", "df['ABI_Mechanic'] = df['ABI Mechanic'].astype('category')\n", "df['Brand'] = df['Brand'].astype('category')\n", "df['Pack_Type'] = df['Pack_Type'].astype('category')\n", "\n", "# Standardize continuous variables\n", "scaler = StandardScaler()\n", "continuous_vars = ['ABI_Duration_Days', 'ABI_Coverage', 'Avg_Temp', 'ABI_vs_Segment_PTC_Index_Agg']\n", "\n", "for var in continuous_vars:\n", "    if var in df.columns:\n", "        df[f'{var}_std'] = scaler.fit_transform(df[[var]])\n", "\n", "print(\"✅ Standardization complete!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. 📈 Exploratory Data Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 📊 Dataset Summary\n", "print(\"📊 DATASET SUMMARY\")\n", "print(\"=\" * 50)\n", "print(f\"  • Final observations: {len(df):,}\")\n", "print(f\"  • Analysis period: {df['ABI Start'].min().strftime('%Y-%m-%d')} to {df['ABI End'].max().strftime('%Y-%m-%d')}\")\n", "\n", "# 🏢 Group distributions\n", "print(f\"\\n🏢 Retailer distribution:\")\n", "retailer_counts = df['Retailer'].value_counts()\n", "for retailer, count in retailer_counts.head(8).items():\n", "    pct = count / len(df) * 100\n", "    print(f\"  • {retailer}: {count:,} promotions ({pct:.1f}%)\")\n", "\n", "print(f\"\\n🏷️ Brand distribution:\")\n", "brand_counts = df['Brand'].value_counts()\n", "for brand, count in brand_counts.head(5).items():\n", "    pct = count / len(df) * 100\n", "    print(f\"  • {brand}: {count:,} promotions ({pct:.1f}%)\")\n", "\n", "print(f\"\\n📦 Pack type distribution:\")\n", "pack_counts = df['Pack_Type'].value_counts()\n", "for pack, count in pack_counts.items():\n", "    pct = count / len(df) * 100\n", "    print(f\"  • {pack}: {count:,} promotions ({pct:.1f}%)\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 📊 KPI Statistics\n", "print(\"📊 KEY KPI STATISTICS\")\n", "print(\"=\" * 50)\n", "\n", "print(f\"  • Duration: {df['ABI_Duration_Days'].mean():.1f} ± {df['ABI_Duration_Days'].std():.1f} days\")\n", "print(f\"  • Coverage: {df['ABI_Coverage'].mean():.3f} ± {df['ABI_Coverage'].std():.3f} ({df['ABI_Coverage'].mean()*100:.1f}%)\")\n", "print(f\"  • Temperature: {df['Avg_Temp'].mean():.1f} ± {df['Avg_Temp'].std():.1f}°C\")\n", "print(f\"  • Overlapping: {df['Overlapping'].sum():,} promotions ({df['Overlapping'].mean()*100:.1f}%)\")\n", "\n", "# ⏰ Timing distribution\n", "print(f\"\\n⏰ Timing relative to competitors:\")\n", "print(f\"  • Before competitor: {df['Before'].sum():,} ({df['Before'].mean()*100:.1f}%)\")\n", "print(f\"  • Same week: {df['Same_Week'].sum():,} ({df['Same_Week'].mean()*100:.1f}%)\")\n", "print(f\"  • After competitor: {df['After'].sum():,} ({df['After'].mean()*100:.1f}%)\")\n", "\n", "# 🎯 Target variable analysis\n", "print(f\"\\n🎯 Target Variable (ABI MS Uplift Rel):\")\n", "target_stats = df['ABI_MS_Uplift_Rel'].describe()\n", "print(f\"  • Mean: {target_stats['mean']:.3f}\")\n", "print(f\"  • Median: {target_stats['50%']:.3f}\")\n", "print(f\"  • Std: {target_stats['std']:.3f}\")\n", "print(f\"  • Range: {target_stats['min']:.3f} to {target_stats['max']:.3f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 📈 Create visualizations\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 10))\n", "\n", "# Target distribution\n", "axes[0,0].hist(df['ABI_MS_Uplift_Rel'], bins=50, alpha=0.7, color='skyblue')\n", "axes[0,0].set_title('Distribution of ABI MS Uplift (Relative)')\n", "axes[0,0].set_xlabel('Uplift Relative')\n", "axes[0,0].set_ylabel('Frequency')\n", "\n", "# Duration vs Uplift\n", "axes[0,1].scatter(df['ABI_Duration_Days'], df['ABI_MS_Uplift_Rel'], alpha=0.6, color='orange')\n", "axes[0,1].set_title('Duration vs Uplift')\n", "axes[0,1].set_xlabel('Duration (Days)')\n", "axes[0,1].set_ylabel('Uplift Relative')\n", "\n", "# Coverage vs Uplift\n", "axes[1,0].scatter(df['ABI_Coverage'], df['ABI_MS_Uplift_Rel'], alpha=0.6, color='green')\n", "axes[1,0].set_title('Coverage vs Uplift')\n", "axes[1,0].set_xlabel('Coverage')\n", "axes[1,0].set_ylabel('Uplift Relative')\n", "\n", "# Temperature vs Uplift\n", "axes[1,1].scatter(df['Avg_Temp'], df['ABI_MS_Uplift_Rel'], alpha=0.6, color='red')\n", "axes[1,1].set_title('Temperature vs Uplift')\n", "axes[1,1].set_xlabel('Average Temperature')\n", "axes[1,1].set_ylabel('Uplift Relative')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"📈 EDA plots generated!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. 🏗️ Hierarchical Model Development"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 🏗️ Prepare data for modeling\n", "print(\"🏗️ HIERARCHICAL MODEL DEVELOPMENT\")\n", "print(\"=\" * 50)\n", "\n", "# Prepare modeling dataset\n", "model_vars = ['ABI_MS_Uplift_Rel', 'ABI_Duration_Days_std', 'ABI_Coverage_std', \n", "              'Overlapping', 'Same_Week', 'Before', 'After', 'Avg_Temp_std',\n", "              'ABI_vs_Segment_PTC_Index_Agg_std', 'ABI_Mechanic', 'Retailer', \n", "              'Brand', 'Pack_Type']\n", "\n", "df_model = df[model_vars].dropna()\n", "print(f\"📊 Data for modeling: {df_model.shape[0]} observations\")\n", "print(f\"📊 Variables: {len(model_vars)} total\")\n", "\n", "# Show group sizes\n", "print(f\"\\n🏢 Group sizes for hierarchical modeling:\")\n", "print(f\"  • Retailers: {df_model['Retailer'].nunique()} groups\")\n", "print(f\"  • Brands: {df_model['Brand'].nunique()} groups\")\n", "print(f\"  • Pack Types: {df_model['Pack_Type'].nunique()} groups\")\n", "\n", "print(f\"\\n✅ Ready for hierarchical modeling!\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}