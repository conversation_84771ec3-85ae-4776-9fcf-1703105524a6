#!/usr/bin/env python3
"""
Hierarchical Mixed-Effects Model Analysis for ABI Promotion Data
================================================================

This script analyzes the effect of various KPIs on ABI MS Uplift (relative) using
hierarchical/mixed-effects modeling to account for retailer, brand, and pack variability.

Target KPIs to analyze:
- ABI_Duration_Days
- ABI Mechanic
- Overlapping
- Same Week, Before (1-2 wk), After (1-2 wk)
- Avg Temp
- ABI vs Segment PTC Index Agg
- ABI_Coverage

Target Variable: ABI MS Uplift rel

Author: AI Assistant
Date: 2025-07-03
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
from datetime import datetime
import statsmodels.api as sm
import statsmodels.formula.api as smf
from statsmodels.regression.mixed_linear_model import MixedLM
from scipy import stats
from sklearn.preprocessing import StandardScaler
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Suppress warnings for cleaner output
warnings.filterwarnings('ignore')

# Set plotting style
plt.style.use('default')
sns.set_palette("husl")
# Use non-interactive backend
plt.switch_backend('Agg')

def load_and_explore_data(data_path):
    """Load data and perform initial exploration"""
    logger.info("Loading and exploring data...")

    # Load data
    df_raw = pd.read_csv(data_path)
    logger.info(f"Loaded data with shape: {df_raw.shape}")

    # Basic info
    print("="*80)
    print("DATA OVERVIEW")
    print("="*80)
    print(f"Dataset shape: {df_raw.shape}")
    print(f"Columns: {list(df_raw.columns)}")
    print("\nFirst few rows:")
    print(df_raw.head())

    print("\nData types:")
    print(df_raw.dtypes)

    print("\nMissing values:")
    missing_summary = df_raw.isnull().sum()
    print(missing_summary[missing_summary > 0])

    return df_raw

def clean_and_engineer_features(df_raw):
    """Clean data and engineer features for modeling"""
    logger.info("Cleaning data and engineering features...")

    # Start with a copy
    df = df_raw.copy()

    # Handle missing values and infinities
    df.replace([np.inf, -np.inf, ""], np.nan, inplace=True)

    # Remove duplicates (each promo repeated for each competitor)
    print(f"Before deduplication: {len(df)} rows")
    df = df.drop_duplicates()
    print(f"After deduplication: {len(df)} rows")

    # Convert date columns
    df['ABI Start'] = pd.to_datetime(df['ABI Start'])
    df['ABI End'] = pd.to_datetime(df['ABI End'])

    # Create duration in days
    df['ABI_Duration_Days'] = (df['ABI End'] - df['ABI Start']).dt.days

    # Extract brand and pack information from ABI SKU
    df['Brand'] = df['ABI SKU'].str.extract(r'^([A-Z]+)')[0]
    df['Pack_12_15'] = df['ABI SKU'].str.contains(r'\(12-15\)', regex=False).astype(int)
    df['Pack_20_24'] = df['ABI SKU'].str.contains(r'\(20-24\)', regex=False).astype(int)
    df['Pack_Type'] = np.where(df['Pack_12_15'] == 1, '12-15',
                              np.where(df['Pack_20_24'] == 1, '20-24', 'Other'))

    # Create timing variables as requested
    df['Before'] = ((df['1 wk before'].fillna(0) == 1) |
                   (df['2 wk before'].fillna(0) == 1)).astype(int)
    df['After'] = ((df['1 wk after'].fillna(0) == 1) |
                  (df['2 wk after'].fillna(0) == 1)).astype(int)
    df['Same_Week'] = df['Same Week'].fillna(0).astype(int)

    # Convert depth buckets to numeric (midpoint)
    depth_mapping = {
        '<20%': 0.15,
        '21%-25%': 0.23,
        '26%-30%': 0.28,
        '31%-33%': 0.32,
        '34%+': 0.36
    }
    df['ABI_Depth_Numeric'] = df['ABI Depth'].map(depth_mapping)

    # Clean and prepare key variables
    df['ABI_Coverage'] = pd.to_numeric(df['ABI Coverage'], errors='coerce')
    df['Overlapping'] = pd.to_numeric(df['Overlapping'], errors='coerce').fillna(0)
    df['Avg_Temp'] = pd.to_numeric(df['Avg Temp'], errors='coerce')
    df['ABI_vs_Segment_PTC_Index_Agg'] = pd.to_numeric(df['ABI vs Segment PTC Index Agg'], errors='coerce')

    # Target variable - handle infinities
    df['ABI_MS_Uplift_Rel'] = pd.to_numeric(df['ABI MS Promo Uplift - rel'], errors='coerce')

    # Remove rows with missing target variable
    df = df.dropna(subset=['ABI_MS_Uplift_Rel'])

    # Handle extreme outliers in target (cap at 95th percentile * 3)
    uplift_95 = df['ABI_MS_Uplift_Rel'].quantile(0.95)
    df['ABI_MS_Uplift_Rel'] = np.where(df['ABI_MS_Uplift_Rel'] > uplift_95 * 3,
                                      uplift_95 * 3, df['ABI_MS_Uplift_Rel'])

    # Convert categorical variables
    df['Retailer'] = df['Retailer'].astype('category')
    df['ABI_Mechanic'] = df['ABI Mechanic'].astype('category')
    df['Brand'] = df['Brand'].astype('category')
    df['Pack_Type'] = df['Pack_Type'].astype('category')

    # Standardize continuous variables for better convergence
    continuous_vars = ['ABI_Duration_Days', 'ABI_Coverage', 'Avg_Temp', 'ABI_vs_Segment_PTC_Index_Agg']
    scaler = StandardScaler()

    for var in continuous_vars:
        if var in df.columns:
            df[f'{var}_std'] = scaler.fit_transform(df[[var]])

    print("\n" + "="*80)
    print("FEATURE ENGINEERING SUMMARY")
    print("="*80)
    print(f"Final dataset shape: {df.shape}")
    print(f"Brands: {df['Brand'].value_counts()}")
    print(f"Retailers: {df['Retailer'].value_counts()}")
    print(f"Pack Types: {df['Pack_Type'].value_counts()}")
    print(f"Mechanics: {df['ABI_Mechanic'].value_counts()}")

    return df

def perform_eda(df):
    """Perform exploratory data analysis"""
    logger.info("Performing exploratory data analysis...")

    print("\n" + "="*80)
    print("EXPLORATORY DATA ANALYSIS")
    print("="*80)

    # Target variable distribution
    print("\nTarget Variable (ABI_MS_Uplift_Rel) Summary:")
    print(df['ABI_MS_Uplift_Rel'].describe())

    # Create visualizations
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))

    # Target distribution
    axes[0,0].hist(df['ABI_MS_Uplift_Rel'], bins=50, alpha=0.7)
    axes[0,0].set_title('Distribution of ABI MS Uplift (Relative)')
    axes[0,0].set_xlabel('Uplift Relative')
    axes[0,0].set_ylabel('Frequency')

    # Duration vs Uplift
    axes[0,1].scatter(df['ABI_Duration_Days'], df['ABI_MS_Uplift_Rel'], alpha=0.6)
    axes[0,1].set_title('Duration vs Uplift')
    axes[0,1].set_xlabel('Duration (Days)')
    axes[0,1].set_ylabel('Uplift Relative')

    # Coverage vs Uplift
    axes[1,0].scatter(df['ABI_Coverage'], df['ABI_MS_Uplift_Rel'], alpha=0.6)
    axes[1,0].set_title('Coverage vs Uplift')
    axes[1,0].set_xlabel('Coverage')
    axes[1,0].set_ylabel('Uplift Relative')

    # Temperature vs Uplift
    axes[1,1].scatter(df['Avg_Temp'], df['ABI_MS_Uplift_Rel'], alpha=0.6)
    axes[1,1].set_title('Temperature vs Uplift')
    axes[1,1].set_xlabel('Average Temperature')
    axes[1,1].set_ylabel('Uplift Relative')

    plt.tight_layout()
    plt.savefig('eda_plots.png', dpi=300, bbox_inches='tight')
    plt.close()

    # Correlation matrix for continuous variables
    continuous_cols = ['ABI_MS_Uplift_Rel', 'ABI_Duration_Days', 'ABI_Coverage',
                      'Avg_Temp', 'ABI_vs_Segment_PTC_Index_Agg', 'Overlapping']

    corr_matrix = df[continuous_cols].corr()

    plt.figure(figsize=(10, 8))
    sns.heatmap(corr_matrix, annot=True, cmap='coolwarm', center=0,
                square=True, fmt='.3f')
    plt.title('Correlation Matrix of Continuous Variables')
    plt.tight_layout()
    plt.savefig('correlation_matrix.png', dpi=300, bbox_inches='tight')
    plt.close()

    # Group-level summaries
    print("\nGroup-level summaries:")
    print("\nBy Retailer:")
    retailer_summary = df.groupby('Retailer')['ABI_MS_Uplift_Rel'].agg(['count', 'mean', 'std'])
    print(retailer_summary)

    print("\nBy Brand:")
    brand_summary = df.groupby('Brand')['ABI_MS_Uplift_Rel'].agg(['count', 'mean', 'std'])
    print(brand_summary)

    print("\nBy Pack Type:")
    pack_summary = df.groupby('Pack_Type')['ABI_MS_Uplift_Rel'].agg(['count', 'mean', 'std'])
    print(pack_summary)

    print("\nBy Mechanic:")
    mechanic_summary = df.groupby('ABI_Mechanic')['ABI_MS_Uplift_Rel'].agg(['count', 'mean', 'std'])
    print(mechanic_summary)

    return df

def build_hierarchical_models(df):
    """Build hierarchical mixed-effects models"""
    logger.info("Building hierarchical mixed-effects models...")

    print("\n" + "="*80)
    print("HIERARCHICAL MODEL DEVELOPMENT")
    print("="*80)

    # Prepare data for modeling - remove any remaining NaN values
    model_vars = ['ABI_MS_Uplift_Rel', 'ABI_Duration_Days_std', 'ABI_Coverage_std',
                  'Overlapping', 'Same_Week', 'Before', 'After', 'Avg_Temp_std',
                  'ABI_vs_Segment_PTC_Index_Agg_std', 'ABI_Mechanic', 'Retailer',
                  'Brand', 'Pack_Type']

    df_model = df[model_vars].dropna()
    print(f"Data for modeling: {df_model.shape[0]} observations")

    models = {}

    # Model 1: Simple fixed effects only (baseline)
    print("\n" + "-"*60)
    print("MODEL 1: Fixed Effects Only (Baseline)")
    print("-"*60)

    try:
        formula_fixed = """ABI_MS_Uplift_Rel ~ ABI_Duration_Days_std + ABI_Coverage_std +
                          Overlapping + Same_Week + Before + After + Avg_Temp_std +
                          ABI_vs_Segment_PTC_Index_Agg_std + C(ABI_Mechanic)"""

        model_fixed = smf.ols(formula_fixed, data=df_model).fit()
        models['fixed_only'] = model_fixed

        print("Fixed Effects Model Summary:")
        print(model_fixed.summary())
        print(f"R-squared: {model_fixed.rsquared:.4f}")
        print(f"AIC: {model_fixed.aic:.2f}")

    except Exception as e:
        print(f"Error fitting fixed effects model: {e}")

    # Model 2: Random intercepts by Retailer
    print("\n" + "-"*60)
    print("MODEL 2: Random Intercepts by Retailer")
    print("-"*60)

    try:
        formula_mixed = """ABI_MS_Uplift_Rel ~ ABI_Duration_Days_std + ABI_Coverage_std +
                          Overlapping + Same_Week + Before + After + Avg_Temp_std +
                          ABI_vs_Segment_PTC_Index_Agg_std + C(ABI_Mechanic)"""

        model_retailer = MixedLM.from_formula(formula_mixed, df_model,
                                            groups=df_model["Retailer"]).fit()
        models['retailer_intercept'] = model_retailer

        print("Random Intercepts by Retailer Model Summary:")
        print(model_retailer.summary())

    except Exception as e:
        print(f"Error fitting retailer random intercepts model: {e}")

    return models, df_model

def build_advanced_hierarchical_models(df_model):
    """Build more complex hierarchical models"""
    logger.info("Building advanced hierarchical models...")

    models = {}

    # Model 3: Random intercepts by Brand
    print("\n" + "-"*60)
    print("MODEL 3: Random Intercepts by Brand")
    print("-"*60)

    try:
        formula_mixed = """ABI_MS_Uplift_Rel ~ ABI_Duration_Days_std + ABI_Coverage_std +
                          Overlapping + Same_Week + Before + After + Avg_Temp_std +
                          ABI_vs_Segment_PTC_Index_Agg_std + C(ABI_Mechanic)"""

        model_brand = MixedLM.from_formula(formula_mixed, df_model,
                                         groups=df_model["Brand"]).fit()
        models['brand_intercept'] = model_brand

        print("Random Intercepts by Brand Model Summary:")
        print(model_brand.summary())

    except Exception as e:
        print(f"Error fitting brand random intercepts model: {e}")

    # Model 4: Crossed random effects (Retailer + Brand)
    print("\n" + "-"*60)
    print("MODEL 4: Crossed Random Effects (Retailer + Brand)")
    print("-"*60)

    try:
        formula_mixed = """ABI_MS_Uplift_Rel ~ ABI_Duration_Days_std + ABI_Coverage_std +
                          Overlapping + Same_Week + Before + After + Avg_Temp_std +
                          ABI_vs_Segment_PTC_Index_Agg_std + C(ABI_Mechanic)"""

        # Use variance components for crossed random effects
        vc = {"Brand": "0 + C(Brand)", "Retailer": "0 + C(Retailer)"}

        model_crossed = MixedLM.from_formula(formula_mixed, df_model,
                                           groups=df_model["Brand"],
                                           vc_formula=vc).fit()
        models['crossed_effects'] = model_crossed

        print("Crossed Random Effects Model Summary:")
        print(model_crossed.summary())

    except Exception as e:
        print(f"Error fitting crossed random effects model: {e}")

    # Model 5: Random slopes for key variables
    print("\n" + "-"*60)
    print("MODEL 5: Random Slopes by Retailer")
    print("-"*60)

    try:
        formula_mixed = """ABI_MS_Uplift_Rel ~ ABI_Duration_Days_std + ABI_Coverage_std +
                          Overlapping + Same_Week + Before + After + Avg_Temp_std +
                          ABI_vs_Segment_PTC_Index_Agg_std + C(ABI_Mechanic)"""

        # Random slope for duration by retailer
        model_slopes = MixedLM.from_formula(formula_mixed, df_model,
                                          groups=df_model["Retailer"],
                                          re_formula="~ABI_Duration_Days_std").fit()
        models['random_slopes'] = model_slopes

        print("Random Slopes Model Summary:")
        print(model_slopes.summary())

    except Exception as e:
        print(f"Error fitting random slopes model: {e}")

    return models

def model_diagnostics(models, df_model):
    """Perform model diagnostics and validation"""
    logger.info("Performing model diagnostics...")

    print("\n" + "="*80)
    print("MODEL DIAGNOSTICS AND VALIDATION")
    print("="*80)

    for model_name, model in models.items():
        if model is None:
            continue

        print(f"\n{'-'*60}")
        print(f"DIAGNOSTICS FOR {model_name.upper()}")
        print(f"{'-'*60}")

        try:
            # Get fitted values and residuals
            if hasattr(model, 'fittedvalues'):
                fitted = model.fittedvalues
                residuals = model.resid
            else:
                # For mixed models
                fitted = model.fittedvalues
                residuals = model.resid

            # Residual plots
            fig, axes = plt.subplots(2, 2, figsize=(12, 10))

            # Residuals vs Fitted
            axes[0,0].scatter(fitted, residuals, alpha=0.6)
            axes[0,0].axhline(y=0, color='red', linestyle='--')
            axes[0,0].set_xlabel('Fitted Values')
            axes[0,0].set_ylabel('Residuals')
            axes[0,0].set_title(f'{model_name}: Residuals vs Fitted')

            # Q-Q plot
            stats.probplot(residuals, dist="norm", plot=axes[0,1])
            axes[0,1].set_title(f'{model_name}: Q-Q Plot')

            # Histogram of residuals
            axes[1,0].hist(residuals, bins=30, alpha=0.7)
            axes[1,0].set_xlabel('Residuals')
            axes[1,0].set_ylabel('Frequency')
            axes[1,0].set_title(f'{model_name}: Residual Distribution')

            # Scale-Location plot
            axes[1,1].scatter(fitted, np.sqrt(np.abs(residuals)), alpha=0.6)
            axes[1,1].set_xlabel('Fitted Values')
            axes[1,1].set_ylabel('√|Residuals|')
            axes[1,1].set_title(f'{model_name}: Scale-Location')

            plt.tight_layout()
            plt.savefig(f'diagnostics_{model_name}.png', dpi=300, bbox_inches='tight')
            plt.close()

            # Model fit statistics
            if hasattr(model, 'aic'):
                print(f"AIC: {model.aic:.2f}")
            if hasattr(model, 'bic'):
                print(f"BIC: {model.bic:.2f}")
            if hasattr(model, 'llf'):
                print(f"Log-Likelihood: {model.llf:.2f}")

            # For mixed models, calculate ICC
            if hasattr(model, 'cov_re'):
                try:
                    var_random = np.diag(model.cov_re).sum()
                    var_residual = model.scale
                    icc = var_random / (var_random + var_residual)
                    print(f"Intraclass Correlation (ICC): {icc:.4f}")
                except:
                    print("Could not calculate ICC")

        except Exception as e:
            print(f"Error in diagnostics for {model_name}: {e}")

def interpret_results(models):
    """Interpret and summarize model results"""
    logger.info("Interpreting model results...")

    print("\n" + "="*80)
    print("MODEL RESULTS INTERPRETATION")
    print("="*80)

    # Model comparison
    print("\nMODEL COMPARISON:")
    print("-" * 40)

    comparison_data = []
    for name, model in models.items():
        if model is not None:
            try:
                aic = getattr(model, 'aic', 'N/A')
                bic = getattr(model, 'bic', 'N/A')
                llf = getattr(model, 'llf', 'N/A')
                comparison_data.append({
                    'Model': name,
                    'AIC': aic,
                    'BIC': bic,
                    'Log-Likelihood': llf
                })
            except:
                pass

    if comparison_data:
        comparison_df = pd.DataFrame(comparison_data)
        print(comparison_df.to_string(index=False))

    # Extract and interpret coefficients from best model
    best_model = None
    best_aic = float('inf')

    for name, model in models.items():
        if model is not None and hasattr(model, 'aic'):
            if model.aic < best_aic:
                best_aic = model.aic
                best_model = model
                best_model_name = name

    if best_model is not None:
        print(f"\n\nBEST MODEL: {best_model_name.upper()}")
        print("-" * 50)

        # Fixed effects interpretation
        print("\nFIXED EFFECTS INTERPRETATION:")
        print("-" * 30)

        if hasattr(best_model, 'params'):
            params = best_model.params
            pvalues = best_model.pvalues if hasattr(best_model, 'pvalues') else None

            for param, coef in params.items():
                significance = ""
                if pvalues is not None and param in pvalues:
                    p_val = pvalues[param]
                    if p_val < 0.001:
                        significance = "***"
                    elif p_val < 0.01:
                        significance = "**"
                    elif p_val < 0.05:
                        significance = "*"
                    elif p_val < 0.1:
                        significance = "."

                print(f"{param}: {coef:.4f} {significance}")

        # Random effects interpretation (if applicable)
        if hasattr(best_model, 'random_effects'):
            print("\nRANDOM EFFECTS:")
            print("-" * 15)
            try:
                random_effects = best_model.random_effects
                for group, effects in random_effects.items():
                    print(f"\nGroup {group}:")
                    for effect_name, effect_value in effects.items():
                        print(f"  {effect_name}: {effect_value:.4f}")
            except:
                print("Could not extract random effects details")

# Main execution
if __name__ == "__main__":
    print("Starting Hierarchical Model Analysis...")
    print("="*80)

    # File path
    data_path = "greater than 10 new_test_output_ads_v3.csv"

    try:
        # Step 1: Load and explore data
        df_raw = load_and_explore_data(data_path)

        # Step 2: Clean and engineer features
        df_clean = clean_and_engineer_features(df_raw)

        # Step 3: Exploratory data analysis
        df_clean = perform_eda(df_clean)

        # Step 4: Build basic hierarchical models
        models_basic, df_model = build_hierarchical_models(df_clean)

        # Step 5: Build advanced hierarchical models
        models_advanced = build_advanced_hierarchical_models(df_model)

        # Combine all models
        all_models = {**models_basic, **models_advanced}

        # Step 6: Model diagnostics
        model_diagnostics(all_models, df_model)

        # Step 7: Interpret results
        interpret_results(all_models)

        print("\n" + "="*80)
        print("ANALYSIS COMPLETE!")
        print("="*80)
        print("Check the generated plots and model summaries above.")
        print("Key files generated:")
        print("- eda_plots.png")
        print("- correlation_matrix.png")
        print("- diagnostics_*.png (for each model)")

    except Exception as e:
        logger.error(f"Error in main execution: {e}")
        raise